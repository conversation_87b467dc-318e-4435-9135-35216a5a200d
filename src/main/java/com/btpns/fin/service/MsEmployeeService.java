package com.btpns.fin.service;

import com.btpns.fin.helper.CommonHelper;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.model.UserModel;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;

@Service
public class MsEmployeeService {
    @Autowired
    IMsEmployeeRepository iMsEmployeeRepository;

    @Autowired
    IMsEmployeeHierarchyRepository iMsEmployeeHierarchyRepository;

    @Autowired
    ITrxPUKVendorRepository trxPUKVendorRepository;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    ITrxFuidRequestRepository iTrxFuidRequestRepository;

    @Autowired
    ITrxSetupParamRequestRepository iTrxSetupParamRequestRepository;

    @Autowired
    UserService userService;

    public MsEmployee getMsEmployeeByNik(@Param("nik") String nik){
        return iMsEmployeeRepository.getMsEmployeeByNik(nik);
    }

    public List<MsEmployee> getListPUK(@Param("nik") String nik){
        String nikPUK = "";
        MsEmployee employee = iMsEmployeeRepository.getMsEmployeeByNik(nik);
        if (employee == null){
            TrxPUKVendor employeeVendor = trxPUKVendorRepository.findByNikVendor(nik);
            nikPUK = employeeVendor.getNikPUK();
        }else {
            nikPUK = getNikPUK(employee.getDirectSupervisorNIK(), employee.getNik());
        }

        List<MsEmployee> listPUK = new ArrayList<MsEmployee>();
        while(!nikPUK.equals("0")){
            MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nikPUK);
            if(msEmployee != null) {
                listPUK.add(msEmployee);
                String newNikPuk = getNikPUK(msEmployee.getDirectSupervisorNIK(), nikPUK);
                if(nikPUK.equalsIgnoreCase(newNikPuk)){
                    nikPUK = "0";
                }else{
                    nikPUK = newNikPuk;
                }
            } else {
                nikPUK = "0";
            }
        }
        return listPUK;
    }

    private String getNikPUK(String nikPUK, String nikEmployee) {
        if(!nikPUK.equalsIgnoreCase(nikEmployee)){
            if (nikPUK.equals("0")) {
                MsEmployeeHierarchy employeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nikEmployee);
                if (employeeHierarchy != null) {
                    nikPUK = employeeHierarchy.getDirectSupervisorNik().equals("0") ? employeeHierarchy.getDirectSupervisor2Nik() : employeeHierarchy.getDirectSupervisorNik();;
                }
            }
        }else {
            MsEmployeeHierarchy employeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nikEmployee);
            if (employeeHierarchy != null) {
                if (!employeeHierarchy.getDirectSupervisorNik().equals("0") && !employeeHierarchy.getDirectSupervisorNik().equalsIgnoreCase(nikPUK)){
                    nikPUK = employeeHierarchy.getDirectSupervisorNik();
                }

                if (!employeeHierarchy.getDirectSupervisor2Nik().equals("0") && !employeeHierarchy.getDirectSupervisor2Nik().equalsIgnoreCase(nikPUK)) {
                    nikPUK = employeeHierarchy.getDirectSupervisor2Nik();
                }
            }else {
                nikPUK = "0";
            }
        }
        return nikPUK;
    }

    public MsEmployee getDirectPUK(String nik){
        MsEmployee msEmployee = iMsEmployeeRepository.getMsEmployeeByNik(nik);
        if(msEmployee == null){
            TrxPUKVendor trxPUKVendor = trxPUKVendorRepository.findByNikVendor(nik);
            if(trxPUKVendor != null) {
                msEmployee = Mapper.toMsEmployee(trxPUKVendor);
            }
        }
        MsEmployee pukDirect = null;
        if(msEmployee != null) {
            String nikDirectPuk = msEmployee.getDirectSupervisorNIK();
            if(!nikDirectPuk.equals("0")) {
                pukDirect = iMsEmployeeRepository.getMsEmployeeByNik(nikDirectPuk);
            }
        }
        return pukDirect;
    }

    public List<MsEmployee> getMsEmployee(List<String> nik) {
        if (nik.size() > 0) {
            return iMsEmployeeRepository.getMsEmployee(nik);
        }
        return Collections.emptyList();
    }

    public List<MsEmployee> getTrxPUKVendor(List<String> nik) {
        if (nik.size() > 0) {
            return trxPUKVendorRepository.getTrxPUKVendor(nik)
                    .stream()
                    .map(trxPUKVendor -> {
                        return Mapper.toMsEmployee(trxPUKVendor);
                    }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public String getEmployeeOrVendor(String nik) {
        Map<String, MsEmployee> msEmployeeMap = getMsEmployeeMap(Arrays.asList(nik));
        if (msEmployeeMap != null && msEmployeeMap.get(nik) != null && msEmployeeMap.get(nik).getFullName() != null) {
            return msEmployeeMap.get(nik).getFullName();
        }
        return "";
    }

    public Map<String, MsEmployee> getMsEmployeeMap(List<String> nik) {
        if (nik.size() > 0) {
            Map<String, MsEmployee> msEmployeeMap = new HashMap<>();
            List<MsEmployee> ret = new ArrayList<>();
            List<String> fte = new ArrayList<>();
            List<String> vendor = new ArrayList<>();
            nik = CommonHelper.distinctList(nik);
            nik.forEach(a -> {
                if (a.length() == 8 && a.matches("\\d+")) {
                    fte.add(a);
                } else {
                    vendor.add(a);
                }
            });

            ret = getMsEmployee(nik);
            ret.addAll(getTrxPUKVendor(nik));
            ret.forEach(msEmployee -> {
                msEmployeeMap.put(msEmployee.getNik(), msEmployee);
            });
            return msEmployeeMap;
        }
        return Collections.emptyMap();
    }

    public List<MsEmployee> getMsEmployeeByName(String name) {
        return iMsEmployeeRepository.getMsEmployeeByName(name);
    }

    public List<String> getNIKByName(String name) {
        return getMsEmployeeByName(name)
                .stream()
                .map(MsEmployee::getNik).collect(Collectors.toList());
    }

    public MsEmployee getEmployeeByNik(String nik) {
        MsEmployee msEmployee = new MsEmployee();

        MsEmployee employee = iMsEmployeeRepository.getMsEmployeeByNik(nik);
        if (employee != null){
            msEmployee = employee;
        }else {
            MsEmployeeHierarchy msEmployeeHierarchy = iMsEmployeeHierarchyRepository.getMsEmployeeHierarchyByNik(nik);
            if (msEmployeeHierarchy != null){
                msEmployee.setNik(msEmployeeHierarchy.getNik());
                msEmployee.setFullName(msEmployeeHierarchy.getFullName());
                msEmployee.setOccupationDesc(msEmployeeHierarchy.getOccupationDesc());
            }else {
                TrxPUKVendor trxPUKVendor = trxPUKVendorRepository.findByNikVendor(nik);
                if (trxPUKVendor != null){
                    msEmployee.setNik(trxPUKVendor.getNikVendor());
                    msEmployee.setFullName(trxPUKVendor.getNameVendor());
                    msEmployee.setOccupationDesc(trxPUKVendor.getOccupationDescVendor());
                }
            }
        }

        return msEmployee;
    }

    public List<String> getListSkippedNikPukFuid(TrxFuidApproval fuidApproval) throws Exception {
        Optional<TrxFuidRequest> fuidRequest = iTrxFuidRequestRepository.findById(fuidApproval.getTicketId());
        List<String> skippedPUKs = getListSkippedNikPuksFuid(FORM_FUID, fuidRequest.get(), fuidApproval);

        return skippedPUKs;
    }

    private List<String> getListSkippedNikPuksFuid(String type, TrxFuidRequest fuidRequest, TrxFuidApproval fuidApproval) throws Exception {
        List<String> skippedPUKs = new ArrayList<>();
        List<MsEmployee> pukList = getListPUK(fuidRequest.getNikRequester());

        if (!pukList.isEmpty() && fuidApproval.getPuk2NIK() != null){
            Optional<MsEmployee> foundPuk = pukList.stream().filter(employee -> employee.getNik().equalsIgnoreCase(fuidApproval.getPuk2NIK())).findAny();
            if (foundPuk.isPresent()){
                skippedPUKs = getListSkippedNikPUKs(pukList, fuidApproval.getPuk1NIK(), fuidApproval.getPuk2NIK());
            }else {
                String nik = fuidRequest.getNikRequester();
                String typeKewenanganLimit = fuidRequest.getTipeLimitTransaksi().equalsIgnoreCase(TIPE_LIMIT_TRANSAKSI_TUNAI) || fuidRequest.getTipeLimitTransaksi().equalsIgnoreCase(TIPE_LIMIT_TRANSAKSI_NONTUNAI) ? TIPE_KEWENANGAN_LIMIT_TRANSAKSI : null;
                String[] aplikasi = fuidRequest.getAplikasi().split(",");
                String tujuan = fuidRequest.getTujuan();
                String alasan = fuidRequest.getAlasan();
                Integer isHavingAttachment = !fuidRequest.getAttachment().isEmpty() ? TRUE_FLAG_INT : FALSE_FLAG_INT;
                Boolean isTunai = fuidRequest.getTipeLimitTransaksi().equalsIgnoreCase(TIPE_LIMIT_TRANSAKSI_TUNAI) ? TRUE_FLAG_BOOL : FALSE_FLAG_BOOL;
                Double nominal = fuidRequest.getNominalTransaksi();
                UserModel getApprovalPuk = userService.getDataUser(null, type, nik, typeKewenanganLimit, aplikasi, tujuan, alasan, isHavingAttachment, isTunai, nominal);

                skippedPUKs.add(getApprovalPuk.getPuk().size() >= 2 ? getApprovalPuk.getPuk().stream().filter(puk -> puk.getRole().equalsIgnoreCase("puk2")).map(puk -> puk.getNik()).findFirst().orElse(EMPTY) : EMPTY);
            }
        }

        return skippedPUKs;
    }

    public List<String> getListSkippedNikPukSp(TrxSetupParamApproval spApproval) throws Exception {
        Optional<TrxSetupParamRequest> spRequest = iTrxSetupParamRequestRepository.findById(spApproval.getTicketId());
        List<String> skippedPUKs = getListSkippedNikPuksSP(FORM_SETUP_PARAMETER, spRequest.get(), spApproval);

        return skippedPUKs;
    }

    private List<String> getListSkippedNikPuksSP(String type, TrxSetupParamRequest spRequest, TrxSetupParamApproval spApproval) throws Exception {
        List<String> skippedPUKs = new ArrayList<>();
        List<MsEmployee> pukList = getListPUK(spRequest.getNikRequester());

        if (!pukList.isEmpty() && spApproval.getPuk2NIK() != null){
            Optional<MsEmployee> foundPuk = pukList.stream().filter(employee -> employee.getNik().equalsIgnoreCase(spApproval.getPuk2NIK())).findAny();
            if (foundPuk.isPresent()){
                skippedPUKs = getListSkippedNikPUKs(pukList, spApproval.getPuk1NIK(), spApproval.getPuk2NIK());
            }else {
                String nik = spRequest.getNikRequester();
                String[] aplikasi = spRequest.getAplikasi().split(",");

                UserModel getApprovalPuk = userService.getDataUser(null, type, nik, null, aplikasi, null, null, null, null, null);
                skippedPUKs.add(getApprovalPuk.getPuk().size() >= 2 ? getApprovalPuk.getPuk().stream().filter(puk -> puk.getRole().equalsIgnoreCase("puk2")).map(puk -> puk.getNik()).findFirst().orElse(EMPTY) : EMPTY);
            }
        }

        return skippedPUKs;
    }

    private List<String> getListSkippedNikPUKs(List<MsEmployee> pukList, String nikPUK1, String nikPUK2) throws Exception {
        List<String> skippedPUKs = new ArrayList<>();
        for (MsEmployee data : pukList){
            TrxDelegation delegation = trxDelegationService.getTrxDelegationByNikRequester(data.getNik());
            if (nikPUK2.equalsIgnoreCase(data.getNik()) || delegation != null) {
                break;
            }
            if (!nikPUK1.equalsIgnoreCase(data.getNik()) && !nikPUK2.equalsIgnoreCase(data.getNik())) {
                skippedPUKs.add(data.getNik());
            }
        }
        return skippedPUKs;
    }
}
