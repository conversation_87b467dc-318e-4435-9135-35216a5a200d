package com.btpns.fin.service;

import com.btpns.fin.helper.Constants;
import com.btpns.fin.helper.GsonUtil;
import com.btpns.fin.helper.Mapper;
import com.btpns.fin.helper.RequestHelper;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.request.TrxFuidRequestDTO;
import com.btpns.fin.model.request.TrxSetupParamRequestDTO;
import com.btpns.fin.repository.ITrxEmailAudittrailRepository;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseStatus.*;

@Service
public class EmailNotificationService {
    private static final Logger logger = LoggerFactory.getLogger(EmailNotificationService.class);
    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @Value("${upm.tema.notification.topic}")
    private String upmTemaNotificationTopic;

    @Value("${mail.sender}")
    private String emailFrom;

    @Value("${send.data.core.mail.target}")
    private String sendDataCoreEmailTarget;

    @Value("${send.data.core.mail.cc}")
    private String sendDataCoreEmailCC;

    @Autowired(required = false)
    ProducerService producerService;

    @Autowired
    EmailService emailService;

    @Autowired
    MinioService minioService;

    @Autowired
    RequestHelper requestHelper;

    @Autowired
    ITrxEmailAudittrailRepository iTrxEmailAudittrailRepository;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    Mapper mapper;

    public void sendSpesimenToUpmTemaNotification(String nikRequester, TrxFuidRequest trxFuidRequest) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectRequestFuid(trxFuidRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentRequestFuidSpesimen(trxFuidRequest);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateRequestFuidToUpmTemaNotification(String nikRequester, TrxFuidRequest trxFuidRequest, MsEmployee waitingApprovalPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectRequestFuid(trxFuidRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentRequestFuid(trxFuidRequest, waitingApprovalPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateApprovalFuidToUpmTemaNotification(String pukNik, TrxFuidRequest trxFuidRequest, String ccNikDirectPuk, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApprovalFuid(trxFuidRequest.getTicketId());
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(pukNik);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentApprovalFuid(trxFuidRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateRequestSPToUpmTemaNotification(String nikRequester, TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee waitingPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxSetupParamRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectRequestSetupParam(trxSetupParamRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentRequestSetupParam(trxSetupParamRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateApprovalSPToUpmTemaNotification(String pukNik, TrxSetupParamRequestDTO trxSetupParamRequest, String ccNikDirectPuk, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector) {
        MessageHeader messageHeader = buildMessageHeader(trxSetupParamRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApprovalSetupParam(trxSetupParamRequest.getTicketId());
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(pukNik);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentApprovalSetupParam(trxSetupParamRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendApprovalFuidToUpmTemaNotification(TrxFuidRequestDTO trxFuidRequest, List<String> skippedNikPUKs, MsEmployee waitingPUK, HashMap<String, String> mapPUK2Director) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApproval2(trxFuidRequest.getTicketId());
        String emailDestination = mapPUK2Director.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUK2Director.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(trxFuidRequest.getTrxFuidApproval().getPuk2NIK());
        String emailCc = getEmailCc(skippedNikPUKs);
        String emailMessage = requestHelper.createContentApproval2Fuid(trxFuidRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public String getEmailCc(List<String> skippedNikPUKs) {
        String emailCc = "";
        for (String nik : skippedNikPUKs) {
            if (emailCc.equals(EMPTY)) {
                emailCc = requestHelper.getEmailUsingNik(nik);
            } else {
                emailCc = emailCc.concat(", ").concat(requestHelper.getEmailUsingNik(nik));
            }
        }
        return emailCc;
    }

    public void sendApprovedFuidToUpmTemaNotification(String nikRequester, TrxFuidRequestDTO trxFuidRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApproved(trxFuidRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentFuidApproved(trxFuidRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateRejectedFuidToUpmTemaNotification(String nikRequester, TrxFuidRequestDTO trxFuidRequest, MsEmployee rejectedPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxFuidRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectRejected(trxFuidRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentFuidRejected(trxFuidRequest, rejectedPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendApprovalSPToUpmTemaNotification(TrxSetupParamRequestDTO trxSetupParamRequest, List<String> skippedNikPUKs, MsEmployee waitingPUK, HashMap<String, String> mapPUK2Director) {
        MessageHeader messageHeader = buildMessageHeader(trxSetupParamRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApproval2(trxSetupParamRequest.getTicketId());
        String emailDestination = mapPUK2Director.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUK2Director.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(trxSetupParamRequest.getTrxSetupParamApproval().getPuk2NIK());
        String emailCc = getEmailCc(skippedNikPUKs);
        String emailMessage = requestHelper.createContentApproval2SetupParam(trxSetupParamRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendApprovedSPToUpmTemaNotification(String nikRequester, TrxSetupParamRequestDTO trxSetupParamRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxSetupParamRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectApproved(trxSetupParamRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentSetupParamApproved(trxSetupParamRequest, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateRejectedSPToUpmTemaNotification(String nikRequester, TrxSetupParamRequestDTO trxSetupParamRequest, MsEmployee rejectedPUK) {
        MessageHeader messageHeader = buildMessageHeader(trxSetupParamRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectRejected(trxSetupParamRequest.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentSetupParamRejected(trxSetupParamRequest, rejectedPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateDelegation(TrxDelegation trxDelegation) {
        MessageHeader messageHeader = buildMessageHeader(trxDelegation.getDelegationId());

        String emailSubject = requestHelper.createSubjectEmailDelegatedPUK();
        String emailDestination = requestHelper.getEmailUsingNik(trxDelegation.getNikDelegation());
        String emailMessage = requestHelper.createContentEmailDelegatedPUK(trxDelegation);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateDelegatedFU(String pukNik, ContentEmail contentEmail, String ccNikDirectPuk, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector) {
        MessageHeader messageHeader = buildMessageHeader(contentEmail.getNomorTiket());

        String emailSubject = requestHelper.createSubjectApprovalFuid(contentEmail.getNomorTiket());
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(pukNik);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentDelegatedApprovalFuid(contentEmail, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreateDelegatedSP(String pukNik, ContentEmail contentEmail, String ccNikDirectPuk, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector) {
        MessageHeader messageHeader = buildMessageHeader(contentEmail.getNomorTiket());

        String emailSubject = requestHelper.createSubjectApprovalSetupParam(contentEmail.getNomorTiket());
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(pukNik);
        String emailCc = !ccNikDirectPuk.equalsIgnoreCase(EMPTY) ? requestHelper.getEmailUsingNik(ccNikDirectPuk) : EMPTY;
        String emailMessage = requestHelper.createContentDelegatedApprovalSetupParam(contentEmail, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendRequestUPMInprogressToUpmTemaNotification(String nikRequester, MsEmployee upmInfo, ContentEmail contentEmail) {
        MessageHeader messageHeader = buildMessageHeader(contentEmail.getNomorTiket());

        String emailSubject = requestHelper.createSubjectUPMInprogress(contentEmail.getNomorTiket());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentUPMInprogress(contentEmail, upmInfo);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendRequestUPMDoneToUpmTemaNotification(String nikRequester, MsEmployee upmInfo, EmailUpmModel emailUpmModel, ContentEmail contentEmail) {
        MessageHeader messageHeader = buildMessageHeader(contentEmail.getNomorTiket());

        String emailSubject = requestHelper.createSubjectUPMDone(contentEmail.getNomorTiket());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailCc = emailUpmModel.isSP() ? requestHelper.getEmailUsingNik(emailUpmModel.getPuk1NikSP()) + ";" + requestHelper.getEmailUsingNik(emailUpmModel.getPuk2NikSP()) : EMPTY;
        String emailMessage = requestHelper.createContentUPMDone(emailUpmModel, contentEmail, upmInfo);
        List<AttachmentModel> emailAttachments = contentEmail.getAttachmentUPM() != null && contentEmail.getAttachmentUPM().size() > 0 ? contentEmail.getAttachmentUPM() : Collections.EMPTY_LIST;
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, emailAttachments);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendRequestUPMRejectToUpmTemaNotification(String nikRequester, MsEmployee upmInfo, EmailUpmModel emailUpmModel, ContentEmail contentEmail) {
        MessageHeader messageHeader = buildMessageHeader(contentEmail.getNomorTiket());

        String emailSubject = requestHelper.createSubjectUPMReject(contentEmail.getNomorTiket());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentUPMReject(emailUpmModel, contentEmail, upmInfo);
        List<AttachmentModel> emailAttachments = contentEmail.getAttachmentUPM() != null && contentEmail.getAttachmentUPM().size() > 0 ? contentEmail.getAttachmentUPM() : Collections.EMPTY_LIST;
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, emailAttachments);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendCreatePostComment(String nikRequester, TrxComment trxComment, List<AttachmentModel> attachmentList) {
        MessageHeader messageHeader = buildMessageHeader(trxComment.getCommentId());

        String emailSubject = requestHelper.createSubjectUPMComment(trxComment.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentUPMComment(trxComment);
        List<AttachmentModel> emailAttachments = attachmentList != null && attachmentList.size() > 0 ? attachmentList : Collections.EMPTY_LIST;
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, emailAttachments);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public MessageHeader buildMessageHeader(String key) {
        MessageHeader messageHeader = new MessageHeader();

        messageHeader.setRequestId(UUID.randomUUID().toString());
        messageHeader.setKey(key);
        messageHeader.setMessageType(NOTIFICATION_MESSAGE_TYPE);

        return messageHeader;
    }

    public void sendUpmTemaNotification(MessageHeader messageHeader, EmailNotification emailNotification) {
        if (producerService != null) {
            producerService.sendMessage(upmTemaNotificationTopic, messageHeader, gson.toJson(emailNotification), messageHeader.getKey());
        } else {
            logger.warn("ProducerService is not available (Kafka is disabled). Cannot send notification to topic: {}", upmTemaNotificationTopic);
        }
    }

    public void sendEmail(EmailNotification emailNotification, String requestId) {
        try {
            boolean response = emailService.sendEmail(requestHelper.createEmailHeader(), buildContentEmail(emailNotification));

            Optional<TrxEmailAudittrail> trxEmailAudittrail = iTrxEmailAudittrailRepository.findByRequestId(requestId);
            if (trxEmailAudittrail.isPresent()){
                updateTrxEmailAudittrail(trxEmailAudittrail.get(), response);
            }else {
                saveTrxEmailAudittrail(mapToTrxEmailAudittrail(emailNotification, response));
            }
        } catch (Exception e) {
            logger.error("Failed to send email {} ", emailNotification, e);
        }
    }

    private void updateTrxEmailAudittrail(TrxEmailAudittrail trxEmailAudittrail, boolean response) {
        TrxEmailAudittrail tea = trxEmailAudittrail;

        tea.setUpdatedAt(LocalDateTime.now());
        tea.setStatus(response ? SUCCESS.toString() : FAILED.toString());

        iTrxEmailAudittrailRepository.save(tea);
    }

    private TrxEmailAudittrail mapToTrxEmailAudittrail(EmailNotification emailNotification, boolean response) {
        TrxEmailAudittrail tea = new TrxEmailAudittrail();

        tea.setRequestId(generateRequestId());
        tea.setCreatedAt(LocalDateTime.now());
        tea.setUpdatedAt(LocalDateTime.now());
        tea.setEmailSubject(emailNotification.getEmailSubject());
        tea.setEmailDestination(emailNotification.getEmailDestination());
        tea.setEmailCc(emailNotification.getEmailCc());
        tea.setEmailBcc(emailNotification.getEmailBcc());
        tea.setEmailMessage(emailNotification.getEmailMessage());
        tea.setEmailAttachments(enrichAttachmentString(emailNotification.getEmailAttachments()));
        tea.setStatus(response ? SUCCESS.toString() : FAILED.toString());

        return tea;
    }

    private String generateRequestId() {
        String requestId = "";

        String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
        String lastRequestId = iTrxEmailAudittrailRepository.getLastRequestId();

        if (lastRequestId == null) {
            requestId = "RE" + currDtTicket + "0001";
        } else {
            String strlastDate = lastRequestId.substring(2, 8);
            if (strlastDate.equals(currDtTicket)) {
                requestId = "RE" + currDtTicket + String.format("%04d", Integer.parseInt(lastRequestId.substring(8, 12)) + 1);
            } else {
                requestId = "RE" + currDtTicket + "0001";
            }
        }

        return requestId;
    }

    private void saveTrxEmailAudittrail(TrxEmailAudittrail trxEmailAudittrail) {
        iTrxEmailAudittrailRepository.save(trxEmailAudittrail);
    }

    private BodyEmail buildContentEmail(EmailNotification emailNotification) throws Exception {
        BodyEmail bodyEmail = new BodyEmail();

        bodyEmail.setTo(emailNotification.getEmailDestination());
        bodyEmail.setMessage(emailNotification.getEmailMessage());
        bodyEmail.setType(Constants.TYPE_MESSAGING_EMAIL);
        bodyEmail.setFrom(emailFrom);
        bodyEmail.setAttachment(enrichAttachment(emailNotification.getEmailAttachments()));
        bodyEmail.setSubject(emailNotification.getEmailSubject());
        bodyEmail.setCc(getValidEmailCc(emailNotification.getEmailCc()));
        bodyEmail.setBcc(emailNotification.getEmailBcc());

        return bodyEmail;
    }

    private String getValidEmailCc(String emailCc) {
        return Arrays.stream(emailCc.split(";")).map(str -> str.trim()).filter(str -> !EMAIL_DUMMY_USER_TEMA.contains(str)).collect(Collectors.joining(";"));
    }

    private String enrichAttachmentString(List<AttachmentModel> emailAttachments) {
        String result = EMPTY;
        if (!emailAttachments.isEmpty() && emailAttachments.size() > 0){
            result = new Gson().toJson(emailAttachments, new TypeToken<ArrayList<AttachmentModel>>(){}.getType());
        }
        return result;
    }

    private List<Attachment> enrichAttachment(List<AttachmentModel> emailAttachments) throws Exception {
        List<Attachment> attachmentList = new ArrayList<>();

        if (!emailAttachments.isEmpty() && emailAttachments.size() > 0) {
            for (AttachmentModel attachment : emailAttachments) {
                attachmentList.add(setAttachmentFromAttachmentModel(attachment));
            }
        }

        return attachmentList;
    }

    private Attachment setAttachmentFromAttachmentModel(AttachmentModel attachmentModel) throws Exception {
        String base64 = minioService.getBase64FromFilePathMinio(attachmentModel.getContentFilePath());
        Attachment attachment = new Attachment();

        attachment.setFilename(attachmentModel.getContentFileName());
        attachment.setMimetype(attachmentModel.getContentType());
        attachment.setBase64(base64);

        return attachment;
    }

    public void sendUserExpiredTemaNotification(TrxExpiredFuid trxExpiredFuid) {
        MessageHeader messageHeader = buildMessageHeader(trxExpiredFuid.getTicketId());

        String emailSubject = requestHelper.createSubjectExpiredTicket(trxExpiredFuid.getTicketId());
        String emailDestination = requestHelper.getEmailUsingNik(trxExpiredFuid.getNik());
        String emailMessage = requestHelper.createContentExpiredTicket(trxExpiredFuid);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendUserUARRequestNotification(TrxUARRequest uarRequest, String appDesc, String paramDetailDesc, Map<String, MsHolidayList> holidayMap) {
        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARUserConfirmation(uarRequest, appDesc);
        String emailDestination = requestHelper.getEmailUsingNik(uarRequest.getNik());
        String emailMessage = requestHelper.createContentUARUserConfirmation(uarRequest, appDesc, paramDetailDesc, holidayMap);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendPukUARApprovalNotification(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARPUKApproval(uarRequest, appDesc);
        String emailDestination = requestHelper.getEmailUsingNik(uarRequest.getTrxUARApproval().getPukNik());
        String emailMessage = requestHelper.createContentUARPUKApproval(uarRequest, appDesc, currentStatusDesc);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendUserUARApprovedNotification(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARPUKApproved(uarRequest, appDesc);
        String emailDestination = requestHelper.getEmailUsingNik(uarRequest.getNik());
        String emailMessage = requestHelper.createContentUARPUKApproved(uarRequest, appDesc, currentStatusDesc);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendUserUARDoneNotification(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc) {
        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARDone(uarRequest, appDesc);
        String emailDestination = requestHelper.getEmailUsingNik(uarRequest.getNik());
        String emailMessage = requestHelper.createContentUARDone(uarRequest, appDesc, currentStatusDesc);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendUserUARRejectedNotification(TrxUARRequest uarRequest, String appDesc, String currentStatusDesc, String rejectedBy, String notes) {
        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARRejected(uarRequest, appDesc, rejectedBy);
        String emailDestination = requestHelper.getEmailUsingNik(uarRequest.getNik());
        String emailMessage = requestHelper.createContentUARRejected(uarRequest, appDesc, currentStatusDesc, rejectedBy, notes);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    public void sendUARReminderNotification(TrxUARRequest uarRequest, String appDesc, String paramDetailDesc, Map<String, MsHolidayList> holidayMap) {
        boolean isPendingUser = STATUS_PENDING_USER.equals(uarRequest.getTrxUARApproval().getCurrentState()) || CURR_STATUS_REJECTED.equals(uarRequest.getTrxUARApproval().getCurrentState());

        MessageHeader messageHeader = buildMessageHeader(uarRequest.getTicketId());

        String emailSubject = requestHelper.createSubjectUARReminder(uarRequest, appDesc);
        String emailDestination = getUARReminderEmailDestination(uarRequest, isPendingUser);
        String emailCc = isPendingUser ? EMPTY : requestHelper.getEmailUsingNik(uarRequest.getNik());
        String emailMessage = createUARReminderEmailMessage(uarRequest, appDesc, paramDetailDesc, holidayMap, isPendingUser);

        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.emptyList());

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    private String getUARReminderEmailDestination(TrxUARRequest uarRequest, boolean isPendingUser) {
        return isPendingUser ? requestHelper.getEmailUsingNik(uarRequest.getNik()) : requestHelper.getEmailUsingNik(uarRequest.getTrxUARApproval().getPukNik());
    }

    private String createUARReminderEmailMessage(TrxUARRequest uarRequest, String appDesc, String paramDetailDesc, Map<String, MsHolidayList> holidayMap, boolean isPendingUser) {
        if (isPendingUser) {
            return requestHelper.createContentUARUserConfirmation(uarRequest, appDesc, paramDetailDesc, holidayMap);
        } else {
            return requestHelper.createContentUARPUKApproval(uarRequest, appDesc, paramDetailDesc);
        }
    }

    public void sendDataCoreSystemToUpmTemaNotification(AttachmentModel prospera, AttachmentModel t24) {
        MessageHeader messageHeader = buildMessageHeader(generateKeyHeaderSendDataCore());

        String emailSubject = requestHelper.createSubjectAndContentDataCoreSystem();
        String emailDestination = sendDataCoreEmailTarget;
        String emailCc = sendDataCoreEmailCC;
        String emailMessage = requestHelper.createSubjectAndContentDataCoreSystem();
        List<AttachmentModel> emailAttachments = new ArrayList<>();
        emailAttachments.add(prospera);
        emailAttachments.add(t24);

        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, emailAttachments);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    private String generateKeyHeaderSendDataCore() {
        StringBuilder sb = new StringBuilder();
        sb.append(DATA_CORE_SYSTEM)
                .append("-")
                .append(LocalDate.now());
        return sb.toString();
    }

    public void sendReminderEmailTicketFuidWaitingApprovalPUK(TrxFuidApproval trxFuidApproval, PUKApprovalReminder pukApprovalReminder, MsEmployee waitingPUK, String ccNIKPUK, HashMap<String, String> mapPUKDirector, Boolean isWaitingPuk1) {
        MessageHeader messageHeader = buildMessageHeader(pukApprovalReminder.getTicketId());

        String emailSubject = requestHelper.createSubjectReminderApproval(pukApprovalReminder.getTicketId(), trxFuidApproval.getPuk1ApprovalReminder(), trxFuidApproval.getPuk2ApprovalReminder(), isWaitingPuk1);
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(waitingPUK.getNik());
        String emailCc = ccNIKPUK;
        String emailMessage = buildContentApprovalFuid(waitingPUK, pukApprovalReminder);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    private String buildContentApprovalFuid(MsEmployee waitingPUK, PUKApprovalReminder pukApprovalReminder) {
        String aplikasi = "";
        String[] splitAplikasi = pukApprovalReminder.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        aplikasi = msta.toString().replaceAll("\\[|\\]", "");

        return requestHelper.createContentReminderApprovalFuid(pukApprovalReminder, waitingPUK, aplikasi);
    }

    public void sendReminderEmailTicketSPWaitingApprovalPUK(TrxSetupParamApproval trxSetupParamApproval, PUKApprovalReminder pukApprovalReminder, MsEmployee waitingPUK, String ccNIKPUK, HashMap<String, String> mapPUKDirector, Boolean isWaitingPuk1) {
        MessageHeader messageHeader = buildMessageHeader(pukApprovalReminder.getTicketId());

        String emailSubject = requestHelper.createSubjectReminderApproval(pukApprovalReminder.getTicketId(), trxSetupParamApproval.getPuk1ApprovalReminder(), trxSetupParamApproval.getPuk2ApprovalReminder(), isWaitingPuk1);
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(waitingPUK.getNik());
        String emailCc = ccNIKPUK;
        String emailMessage = buildContentApprovalSetupParam(pukApprovalReminder, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    private String buildContentApprovalSetupParam(PUKApprovalReminder pukApprovalReminder, MsEmployee waitingPUK) {
        String aplikasi = "";
        String[] splitAplikasi = pukApprovalReminder.getAplikasi().split(",");
        List<String> listAplikasi = Arrays.asList(splitAplikasi);
        List<String> msta = msTemaApplicationService.getMsTemaApplicationList(listAplikasi);
        aplikasi = msta.toString().replaceAll("\\[|\\]", "");

        return requestHelper.createContentReminderApprovalSetupParam(pukApprovalReminder, waitingPUK, aplikasi);
    }

    public void sendReportDetailUPMCsvToEmail(AttachmentModel report, String nikRequester, String reportType, String startDate, String endDate) {
        MessageHeader messageHeader = buildMessageHeader(generateKeyHeaderSendReportDetailUPM());

        String emailSubject = requestHelper.createSubjectSendReportPermohonan(reportType, startDate, endDate);
        String emailDestination = sendDataCoreEmailTarget;
        String emailCc = sendDataCoreEmailCC + "," +requestHelper.getEmailUsingNik(nikRequester);
        String emailMessage = requestHelper.createContentSendReportPermohonan(reportType, startDate, endDate);
        List<AttachmentModel> emailAttachments = new ArrayList<>();
        emailAttachments.add(report);

        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, emailCc, EMPTY, emailMessage, emailAttachments);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }

    private String generateKeyHeaderSendReportDetailUPM() {
        StringBuilder sb = new StringBuilder();
        sb.append(DATA_REPORT_DETAIL_UPM)
                .append("-")
                .append(LocalDate.now());
        return sb.toString();
    }

    public void sendCreatePreviousDelegatedApproval(String ticketId, String fromPUK, MsEmployee waitingPUK, HashMap<String, String> mapPUKDirector) {
        MessageHeader messageHeader = buildMessageHeader(ticketId);

        String emailSubject = requestHelper.createSubjectPreviousDelegatedApproval(ticketId);
        String emailDestination = mapPUKDirector.get(KEY_IS_PUK_DIRECTOR).equals(TRUE_FLAG_STR) ? mapPUKDirector.get(KEY_EMAIL_PUK_DIRECTOR) : requestHelper.getEmailUsingNik(fromPUK);
        String emailMessage = requestHelper.createContentPreviousDelegatedApproval(ticketId, waitingPUK);
        EmailNotification emailNotification = Mapper.toEmailNotification(emailSubject, emailDestination, EMPTY, EMPTY, emailMessage, Collections.EMPTY_LIST);

        sendUpmTemaNotification(messageHeader, emailNotification);
    }
}
