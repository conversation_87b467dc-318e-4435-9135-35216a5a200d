STAGE=south

ELASTIC_APM_SERVICE_NAME=tema-svc
ELASTIC_APM_ENVIRONMENT=south
ELASTIC_APM_LOG_LEVEL=ERROR
ELASTIC_APM_SERVER_URLS=http://apm-twprisma-http-apm-http.nww-monitoring.svc:8200
ELASTIC_APM_VERIFY_SERVER_CERT=false
ELASTIC_APM_APPLICATION_PACKAGES=com.btpns.fin
ELASTIC_APM_TOKEN=

REPLICAS_PODS=5
LIMIT_CPU=500m
LIMIT_MEMORY=668M

HEAP_MIN=32m
HEAP_MAX=1024m
HEAP_STACK=228k

LIQUIBASE_ENABLE=true

MINIO_URI=https://storage.apps.south.syariahbtpn.com
BUCKET_NAME=images
IMAGE_BASE_URI=https://storage.apps.btpnsyariah.com

UPM_TEMA_NOTIFICATION_TOPIC=upm.tema.notification

KAFKA_SERVER=kafka.svc.south.syariahbtpn.com:443
CONSUMER_AUTO_OFFSET_RESET=latest
CONSUMER_HEARTBEAT_INTERVAL=60000
CONSUMER_SESSION_TIMEOUT=180000
CONSUMER_AUTO_COMMIT=false
CONSUMER_CLIENT_ID=tema-svc-prod
CONSUMER_GROUP_ID=tema-svc-prod-group
PRODUCER_CLIENT_ID=tema-svc-prod
PRODUCER_RETRIES=3

URL_FUID_DETAIL=https://tema.apps.btpnsyariah.com/form/fuid/
URL_PARAM_DETAIL=https://tema.apps.btpnsyariah.com/form/fsp/
URL_UAR_DETAIL=https://tema.apps.btpnsyariah.com/rincian-pemantauan-user-id/

MAIL_URL=https://api-v2.apps.btpnsyariah.com/int/notification/3.0.0/
MAIL_API_VERSION=1.3.0
MAIL_SENDER=<<EMAIL>>
SEND_DATA_CORE_MAIL_TARGET=<EMAIL>
SEND_DATA_CORE_MAIL_CC=<EMAIL>

PROSPERA_ROUTER_URL=https://router-prospera2-south.apps.south.syariahbtpn.com/prosperarest/services/

USER_WHITELIST_MENU_PROSPERA_BY_NIK=all
USER_WHITELIST_MENU_PROSPERA_BY_OFFICECODE=all

MAX_UPM_LIMIT_FOR_BWMP_NOMINAL_INPUT=100000000
QUERY_LIMIT=100

SPRINGDOC_UI_TOGGLE=false

HEADER_API_KEY=246eb17a0b8524853c8668f00b357832

